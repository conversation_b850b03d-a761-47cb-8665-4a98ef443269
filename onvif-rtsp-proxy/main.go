package main

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/bluenviron/gortsplib/v4"
	"github.com/bluenviron/gortsplib/v4/pkg/base"
	"github.com/bluenviron/gortsplib/v4/pkg/description"
	"github.com/bluenviron/gortsplib/v4/pkg/format"
	"github.com/pion/rtp"
)

const (
	// ONVIF backchannel feature tag as per ONVIF Streaming Spec 5.3.1
	ONVIFBackchannelFeatureTag = "www.onvif.org/ver20/backchannel"

	// Default configuration
	DefaultRTSPPort  = 8555
	DefaultPythonURL = "http://127.0.0.1:8080/audio_input"
	DefaultVideoURL  = "rtsp://127.0.0.1:8554/doorbell"
)

// Config holds the proxy configuration
type Config struct {
	RTSPPort  int
	PythonURL string
	VideoURL  string
}

// ONVIFRTSPProxy implements an ONVIF-compliant RTSP server with backchannel support
type ONVIFRTSPProxy struct {
	config     Config
	server     *gortsplib.Server
	httpClient *http.Client

	mutex               sync.RWMutex
	streams             map[string]*gortsplib.ServerStream
	backchannelSessions map[string]*gortsplib.ServerSession

	// Video source client for forwarding from MediaMTX
	videoClient           *gortsplib.Client
	videoClientMutex      sync.Mutex
	videoForwardingActive bool
	videoForwardingCancel context.CancelFunc
}

// NewONVIFRTSPProxy creates a new ONVIF RTSP proxy instance
func NewONVIFRTSPProxy(config Config) *ONVIFRTSPProxy {
	return &ONVIFRTSPProxy{
		config: config,
		httpClient: &http.Client{
			Timeout: 5 * time.Second,
		},
		streams:             make(map[string]*gortsplib.ServerStream),
		backchannelSessions: make(map[string]*gortsplib.ServerSession),
	}
}

// createMediaDescriptionWithBackchannel creates media description based on MediaMTX source and backchannel support
func (p *ONVIFRTSPProxy) createMediaDescriptionWithBackchannel(withBackchannel bool) *description.Session {
	// Try to get media description from MediaMTX first
	if sourceDesc := p.getMediaMTXDescription(); sourceDesc != nil {
		var medias []*description.Media

		// Always copy video tracks from MediaMTX
		for _, media := range sourceDesc.Medias {
			if media.Type == description.MediaTypeVideo {
				medias = append(medias, media)
			}
		}

		// Always copy existing audio tracks from MediaMTX for outgoing audio
		for _, media := range sourceDesc.Medias {
			if media.Type == description.MediaTypeAudio {
				medias = append(medias, media)
			}
		}

		if withBackchannel {
			// Add backchannel audio track as a separate third track for incoming audio
			// This follows ONVIF Profile T specification requiring separate tracks for bidirectional audio
			backchannelTrack := &description.Media{
				Type:          description.MediaTypeAudio,
				IsBackChannel: true, // CRITICAL: Mark this as a backchannel track for proper SDP generation
				Formats: []format.Format{&format.G711{
					PayloadTyp:   98,    // Use different payload type from outgoing audio
					MULaw:        false, // A-law (PCMA)
					SampleRate:   8000,
					ChannelCount: 1,
				}},
			}
			medias = append(medias, backchannelTrack)
		}

		return &description.Session{Medias: medias}
	}

	// Fallback to simple description if MediaMTX not available
	videoTrack := &description.Media{
		Type: description.MediaTypeVideo,
		Formats: []format.Format{&format.H264{
			PayloadTyp: 96,
		}},
	}

	if withBackchannel {
		// For backchannel: video + outgoing audio + backchannel audio (3 tracks total)
		outgoingAudioTrack := &description.Media{
			Type: description.MediaTypeAudio,
			Formats: []format.Format{&format.G711{
				PayloadTyp:   97,    // Outgoing audio payload type
				MULaw:        false, // A-law (PCMA)
				SampleRate:   8000,
				ChannelCount: 1,
			}},
		}

		backchannelAudioTrack := &description.Media{
			Type:          description.MediaTypeAudio,
			IsBackChannel: true, // CRITICAL: Mark this as a backchannel track for proper SDP generation
			Formats: []format.Format{&format.G711{
				PayloadTyp:   98,    // Backchannel audio payload type
				MULaw:        false, // A-law (PCMA)
				SampleRate:   8000,
				ChannelCount: 1,
			}},
		}

		return &description.Session{
			Medias: []*description.Media{videoTrack, outgoingAudioTrack, backchannelAudioTrack},
		}
	} else {
		// For non-backchannel: video + audio (2 tracks total)
		audioTrack := &description.Media{
			Type: description.MediaTypeAudio,
			Formats: []format.Format{&format.G711{
				PayloadTyp:   97,    // Use dynamic payload type to avoid conflicts
				MULaw:        false, // A-law (PCMA)
				SampleRate:   8000,
				ChannelCount: 1,
			}},
		}

		return &description.Session{
			Medias: []*description.Media{videoTrack, audioTrack},
		}
	}
}

// getMediaMTXDescription tries to get the media description from MediaMTX
func (p *ONVIFRTSPProxy) getMediaMTXDescription() *description.Session {
	client := &gortsplib.Client{}

	u, err := base.ParseURL(p.config.VideoURL)
	if err != nil {
		return nil
	}

	err = client.Start(u.Scheme, u.Host)
	if err != nil {
		return nil
	}
	defer client.Close()

	desc, _, err := client.Describe(u)
	if err != nil {
		return nil
	}

	return desc
}

// forwardAudioToPython sends received audio data to the Python application
func (p *ONVIFRTSPProxy) forwardAudioToPython(audioData []byte) error {
	req, err := http.NewRequest("POST", p.config.PythonURL, bytes.NewReader(audioData))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	req.Header.Set("Content-Type", "audio/pcm")
	req.Header.Set("Content-Length", strconv.Itoa(len(audioData)))

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send audio to Python: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("Python returned error %d: %s", resp.StatusCode, string(body))
	}

	return nil
}

// handleBackchannelAudio processes incoming backchannel audio from ONVIF clients
func (p *ONVIFRTSPProxy) handleBackchannelAudio(packet *rtp.Packet) {
	audioPayload := packet.Payload

	log.Printf("📤 Received backchannel audio: %d bytes, timestamp: %d, seq: %d",
		len(audioPayload), packet.Timestamp, packet.SequenceNumber)

	if err := p.forwardAudioToPython(audioPayload); err != nil {
		log.Printf("❌ Failed to forward audio to Python: %v", err)
		return
	}

	log.Printf("✅ Audio forwarded to Python successfully")
}

// getOrCreateStream returns existing stream or creates new one for path
func (p *ONVIFRTSPProxy) getOrCreateStream(path string) *gortsplib.ServerStream {
	return p.getOrCreateStreamWithBackchannel(path, false)
}

// getOrCreateStreamWithBackchannel returns existing stream or creates new one with backchannel support
func (p *ONVIFRTSPProxy) getOrCreateStreamWithBackchannel(path string, withBackchannel bool) *gortsplib.ServerStream {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Create different stream keys for backchannel vs non-backchannel
	streamKey := path
	if withBackchannel {
		streamKey = path + "_backchannel"
	}

	if stream, exists := p.streams[streamKey]; exists {
		return stream
	}

	// Create new stream only when needed
	desc := p.createMediaDescriptionWithBackchannel(withBackchannel)

	// Use NewServerStream which should handle initialization internally
	stream := gortsplib.NewServerStream(p.server, desc)

	p.streams[streamKey] = stream

	if withBackchannel {
		log.Printf("🎥 Created backchannel stream for path: %s", path)
	} else {
		log.Printf("🎥 Created stream for path: %s", path)
	}
	return stream
}

// OnConnOpen implements connection open handler
func (p *ONVIFRTSPProxy) OnConnOpen(ctx *gortsplib.ServerHandlerOnConnOpenCtx) {
	log.Printf("🔗 RTSP connection opened from %v", ctx.Conn.NetConn().RemoteAddr())
}

// OnConnClose implements connection close handler
func (p *ONVIFRTSPProxy) OnConnClose(ctx *gortsplib.ServerHandlerOnConnCloseCtx) {
	log.Printf("🔒 RTSP connection closed: %v", ctx.Error)
}

// OnSessionOpen implements session open handler
func (p *ONVIFRTSPProxy) OnSessionOpen(ctx *gortsplib.ServerHandlerOnSessionOpenCtx) {
	log.Printf("📺 RTSP session opened")
}

// OnSessionClose implements session close handler
func (p *ONVIFRTSPProxy) OnSessionClose(ctx *gortsplib.ServerHandlerOnSessionCloseCtx) {
	log.Printf("📺 RTSP session closed: %v", ctx.Error)
}

// OnDescribe implements the DESCRIBE handler
func (p *ONVIFRTSPProxy) OnDescribe(ctx *gortsplib.ServerHandlerOnDescribeCtx) (*base.Response, *gortsplib.ServerStream, error) {
	log.Printf("📋 DESCRIBE request for %s", ctx.Request.URL)

	// Check for ONVIF backchannel requirement
	requireHeader := ctx.Request.Header["Require"]
	supportsBackchannel := false
	for _, req := range requireHeader {
		if strings.Contains(req, ONVIFBackchannelFeatureTag) {
			supportsBackchannel = true
			log.Printf("✅ Client supports ONVIF backchannel: %s", req)
			break
		}
	}

	if !supportsBackchannel {
		log.Printf("⚠️  Client does not support ONVIF backchannel")
	}

	stream := p.getOrCreateStreamWithBackchannel(ctx.Path, supportsBackchannel)

	// Log the SDP we're returning for debugging
	if stream != nil {
		log.Printf("📋 Returning SDP with %d media tracks:", len(stream.Description().Medias))
		for i, media := range stream.Description().Medias {
			if len(media.Formats) > 0 {
				fmt := media.Formats[0]
				trackPurpose := ""
				if supportsBackchannel && len(stream.Description().Medias) == 3 {
					switch i {
					case 0:
						trackPurpose = " (video - sendonly)"
					case 1:
						trackPurpose = " (outgoing audio - sendonly)"
					case 2:
						trackPurpose = " (backchannel audio - recvonly)"
					}
				}
				log.Printf("   Track %d: %s (%T)%s", i, media.Type, fmt, trackPurpose)
			} else {
				log.Printf("   Track %d: %s (no formats)", i, media.Type)
			}
		}
	}

	return &base.Response{StatusCode: base.StatusOK}, stream, nil
}

// OnSetup implements the SETUP handler
func (p *ONVIFRTSPProxy) OnSetup(ctx *gortsplib.ServerHandlerOnSetupCtx) (*base.Response, *gortsplib.ServerStream, error) {
	log.Printf("🔧 SETUP request for path %s", ctx.Path)

	// Check if client supports backchannel
	hasBackchannelReq := false
	if ctx.Request.Header != nil {
		if require := ctx.Request.Header["Require"]; len(require) > 0 {
			for _, req := range require {
				if strings.Contains(req, "www.onvif.org/ver20/backchannel") {
					hasBackchannelReq = true
					break
				}
			}
		}
	}

	// Log setup details
	log.Printf("🔧 SETUP with backchannel support: %v", hasBackchannelReq)

	stream := p.getOrCreateStreamWithBackchannel(ctx.Path, hasBackchannelReq)

	// Log which specific media track is being set up
	if stream != nil {
		desc := stream.Description()
		if desc != nil {
			log.Printf("🔧 Stream has %d total media tracks", len(desc.Medias))
			for i, media := range desc.Medias {
				mediaType := "unknown"
				if media.Type == description.MediaTypeVideo {
					mediaType = "video"
				} else if media.Type == description.MediaTypeAudio {
					if media.IsBackChannel {
						mediaType = "backchannel audio"
					} else {
						mediaType = "outgoing audio"
					}
				}
				log.Printf("🔧 Track %d: %s (IsBackChannel: %v)", i, mediaType, media.IsBackChannel)
			}
		}
	}

	return &base.Response{StatusCode: base.StatusOK}, stream, nil
}

// OnPlay implements the PLAY handler
func (p *ONVIFRTSPProxy) OnPlay(ctx *gortsplib.ServerHandlerOnPlayCtx) (*base.Response, error) {
	log.Printf("▶️  PLAY request for path %s", ctx.Path)

	// Check for backchannel requirement
	requireHeader := ctx.Request.Header["Require"]
	hasBackchannelReq := false
	for _, req := range requireHeader {
		if strings.Contains(req, ONVIFBackchannelFeatureTag) {
			hasBackchannelReq = true
			log.Printf("✅ PLAY with ONVIF backchannel requirement: %s", req)
			break
		}
	}

	if hasBackchannelReq {
		log.Printf("🎤 Setting up backchannel packet handler after video forwarding starts")

		ctx.Session.OnPacketRTPAny(func(medi *description.Media, _ format.Format, pkt *rtp.Packet) {
			if pkt != nil {
				log.Printf("🎤 Received backchannel audio: payload type=%d, length=%d",
					pkt.PayloadType, len(pkt.Payload))
				p.handleBackchannelAudio(pkt)
			}
		})
	}

	// Start video forwarding from MediaMTX (with connection reuse)
	go p.ensureVideoForwarding(ctx.Path)

	return &base.Response{StatusCode: base.StatusOK}, nil
}

// ensureVideoForwarding ensures video forwarding is active, with health checking and reconnection
func (p *ONVIFRTSPProxy) ensureVideoForwarding(path string) {
	p.videoClientMutex.Lock()
	defer p.videoClientMutex.Unlock()

	// If video forwarding is already active, verify the connection is still healthy
	if p.videoForwardingActive {
		if p.isVideoConnectionHealthy() {
			log.Printf("📹 Video forwarding already active and healthy for path: %s", path)
			return
		} else {
			log.Printf("⚠️  Video forwarding connection unhealthy, restarting for path: %s", path)
			p.stopVideoForwardingLocked()
		}
	}

	// Mark as active and start forwarding
	p.videoForwardingActive = true
	go p.startVideoForwarding(path)
}

// isVideoConnectionHealthy checks if the current video connection is still working
func (p *ONVIFRTSPProxy) isVideoConnectionHealthy() bool {
	if p.videoClient == nil {
		return false
	}

	// Try a quick DESCRIBE to test if MediaMTX is still responsive
	testClient := &gortsplib.Client{}
	u, err := base.ParseURL(p.config.VideoURL)
	if err != nil {
		return false
	}

	err = testClient.Start(u.Scheme, u.Host)
	if err != nil {
		return false
	}
	defer testClient.Close()

	_, _, err = testClient.Describe(u)
	return err == nil
}

// stopVideoForwardingLocked stops video forwarding (must be called with videoClientMutex held)
func (p *ONVIFRTSPProxy) stopVideoForwardingLocked() {
	if p.videoForwardingCancel != nil {
		p.videoForwardingCancel()
		p.videoForwardingCancel = nil
	}

	if p.videoClient != nil {
		p.videoClient.Close()
		p.videoClient = nil
	}

	p.videoForwardingActive = false
	log.Printf("📹 Video forwarding stopped and cleaned up")
}

// OnRecord implements the RECORD handler
func (p *ONVIFRTSPProxy) OnRecord(ctx *gortsplib.ServerHandlerOnRecordCtx) (*base.Response, error) {
	log.Printf("⏺️  RECORD request for path %s", ctx.Path)
	// ONVIF backchannel doesn't use RECORD - it uses PLAY with audio packets
	return &base.Response{StatusCode: base.StatusOK}, nil
}

// startVideoForwarding connects to MediaMTX and forwards real video to our stream
func (p *ONVIFRTSPProxy) startVideoForwarding(path string) {
	log.Printf("📹 Starting video forwarding from MediaMTX for path: %s", path)

	// Create cancellation context for this forwarding session
	ctx, cancel := context.WithCancel(context.Background())
	p.videoClientMutex.Lock()
	p.videoForwardingCancel = cancel
	p.videoClientMutex.Unlock()

	// Ensure we reset the active flag when done
	defer func() {
		p.videoClientMutex.Lock()
		p.videoForwardingActive = false
		p.videoClient = nil
		p.videoForwardingCancel = nil
		p.videoClientMutex.Unlock()
		log.Printf("📹 Video forwarding stopped for path: %s", path)
	}()

	// Create client to connect to MediaMTX
	client := &gortsplib.Client{}
	p.videoClientMutex.Lock()
	p.videoClient = client
	p.videoClientMutex.Unlock()

	// Parse MediaMTX URL
	u, err := base.ParseURL(p.config.VideoURL)
	if err != nil {
		log.Printf("❌ Failed to parse video URL %s: %v", p.config.VideoURL, err)
		return
	}

	// Connect to MediaMTX with retry logic
	maxRetries := 3
	for attempt := 1; attempt <= maxRetries; attempt++ {
		select {
		case <-ctx.Done():
			log.Printf("📹 Video forwarding cancelled during connection attempt")
			return
		default:
		}

		err = client.Start(u.Scheme, u.Host)
		if err == nil {
			break
		}

		log.Printf("❌ Failed to connect to MediaMTX (attempt %d/%d): %v", attempt, maxRetries, err)
		if attempt < maxRetries {
			time.Sleep(time.Second * 2)
		}
	}

	if err != nil {
		log.Printf("❌ Failed to connect to MediaMTX after %d attempts", maxRetries)
		return
	}
	defer client.Close()

	// Perform DESCRIBE to get media description
	desc, _, err := client.Describe(u)
	if err != nil {
		log.Printf("URL is %s", u.String())
		log.Printf("❌ Failed to describe MediaMTX stream: %v", err)
		return
	}

	log.Printf("📺 Connected to MediaMTX, received %d media tracks", len(desc.Medias))

	// Setup all media tracks for reading
	for i, media := range desc.Medias {
		log.Printf("📹 Setting up media track %d: %s", i, media.Type)
		_, err := client.Setup(desc.BaseURL, media, 0, 0)
		if err != nil {
			log.Printf("❌ Failed to setup media track %d: %v", i, err)
			continue
		}
	}

	// Setup packet forwarding for all media
	client.OnPacketRTPAny(func(medi *description.Media, forma format.Format, packet *rtp.Packet) {
		// Forward packets to our stream
		stream := p.getOrCreateStream(path)
		if stream != nil && len(stream.Description().Medias) > 0 {
			streamDesc := stream.Description()

			// Forward video to first media (track 0)
			if medi.Type == description.MediaTypeVideo && len(streamDesc.Medias) > 0 {
				videoMedia := streamDesc.Medias[0]
				err := stream.WritePacketRTP(videoMedia, packet)
				if err != nil {
					log.Printf("❌ Failed to forward video packet: %v", err)
				}
			}

			// Forward outgoing audio to second media (track 1) - this is doorbell audio TO client
			if medi.Type == description.MediaTypeAudio && len(streamDesc.Medias) > 1 {
				// This is audio FROM the doorbell (MediaMTX) TO the client
				// Track 1 is outgoing audio (sendonly), Track 2 is backchannel (recvonly)
				outgoingAudioMedia := streamDesc.Medias[1]
				err := stream.WritePacketRTP(outgoingAudioMedia, packet)
				if err != nil {
					log.Printf("❌ Failed to forward outgoing audio packet: %v", err)
				} else {
					// Log occasionally to verify audio forwarding
					if packet.SequenceNumber%500 == 0 {
						log.Printf("🔊 Forwarded outgoing audio packet to client (seq: %d)", packet.SequenceNumber)
					}
				}
			}
		}
	})

	// Start playing
	_, err = client.Play(nil)
	if err != nil {
		log.Printf("❌ Failed to start playing from MediaMTX: %v", err)
		return
	}

	log.Printf("✅ Video forwarding started from MediaMTX")

	// Start periodic health check
	healthTicker := time.NewTicker(10 * time.Second)
	defer healthTicker.Stop()

	// Wait for client to finish or context cancellation
	done := make(chan struct{})
	go func() {
		client.Wait()
		close(done)
	}()

	for {
		select {
		case <-ctx.Done():
			log.Printf("📹 Video forwarding cancelled")
			client.Close()
			return
		case <-done:
			log.Printf("📹 Video forwarding ended naturally")
			return
		case <-healthTicker.C:
			// Periodic health check - if MediaMTX is down, this will help detect it faster
			if !p.isVideoConnectionHealthy() {
				log.Printf("⚠️  MediaMTX health check failed, stopping video forwarding")
				client.Close()
				return
			}
		}
	}
}

// Start initializes and starts the ONVIF RTSP proxy server
func (p *ONVIFRTSPProxy) Start(ctx context.Context) error {
	server := &gortsplib.Server{
		Handler:     p,
		RTSPAddress: fmt.Sprintf(":%d", p.config.RTSPPort),
	}

	p.server = server

	log.Printf("🚀 Starting ONVIF RTSP Proxy on port %d", p.config.RTSPPort)
	log.Printf("📡 Video source: %s", p.config.VideoURL)
	log.Printf("🔗 Python endpoint: %s", p.config.PythonURL)

	return server.StartAndWait()
}

// Stop gracefully shuts down the RTSP proxy
func (p *ONVIFRTSPProxy) Stop() {
	// Stop video forwarding first
	p.videoClientMutex.Lock()
	if p.videoForwardingActive {
		p.stopVideoForwardingLocked()
	}
	p.videoClientMutex.Unlock()

	// Clean up streams
	p.mutex.Lock()
	for path, stream := range p.streams {
		stream.Close()
		delete(p.streams, path)
	}
	p.mutex.Unlock()

	if p.server != nil {
		p.server.Close()
		log.Printf("🛑 ONVIF RTSP Proxy stopped")
	}
}

func main() {
	config := Config{
		RTSPPort:  DefaultRTSPPort,
		PythonURL: DefaultPythonURL,
		VideoURL:  DefaultVideoURL,
	}

	proxy := NewONVIFRTSPProxy(config)

	log.Printf("🌟 ONVIF RTSP Proxy v1.0")
	log.Printf("📖 ONVIF Streaming Specification v23.06 compliant")
	log.Printf("🔧 Configuration: Port=%d, Python=%s, Video=%s",
		config.RTSPPort, config.PythonURL, config.VideoURL)

	if err := proxy.Start(context.Background()); err != nil {
		log.Fatalf("❌ Failed to start ONVIF RTSP Proxy: %v", err)
	}
}
